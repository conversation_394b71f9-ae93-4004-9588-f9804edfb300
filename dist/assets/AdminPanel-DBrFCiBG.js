import{a as M,r as p,o as N,b as R,e as V,f as s,g as l,w as e,j as u,E as o,u as I,x,l as n,t as y,n as T,y as F}from"./index-DuZf145c.js";import{i as w,a as P,_ as j}from"./_plugin-vue_export-helper-BUfy8ct9.js";const Q={class:"admin-panel-container"},q={class:"header"},H={class:"stat-content"},J={class:"stat-number"},K={class:"stat-content"},O={class:"stat-number"},W={class:"stat-content"},X={class:"stat-number"},Y={class:"import-section"},Z={class:"upload-actions"},$={class:"import-section"},tt={class:"upload-actions"},st={class:"actions"},et=M({__name:"AdminPanel",setup(lt){const b=I(),f=p({total:0,guozi:0,dayi:0}),d=p([]),r=p([]),v=p(!1),g=p(!1),_=async()=>{try{const a=await w.getStatistics();a.code===200&&(f.value=a.data)}catch(a){console.error("获取统计信息失败:",a)}},U=(a,t)=>{d.value=t},S=(a,t)=>{r.value=t},h=async()=>{if(d.value.length===0){o.warning("请先选择要导入的文件");return}try{await x.confirm("确定要导入国资系统数据吗？","确认导入",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),v.value=!0;const a=d.value[0].raw,t=await w.importExcel(a,"国资系统");t.code===200?(o.success("国资系统数据导入成功"),C(),await _()):o.error(t.message||"导入失败")}catch(a){a!=="cancel"&&(console.error("导入失败:",a),o.error("导入失败："+(a.message||"未知错误")))}finally{v.value=!1}},A=async()=>{if(r.value.length===0){o.warning("请先选择要导入的文件");return}try{await x.confirm("确定要导入大仪平台数据吗？","确认导入",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),g.value=!0;const a=r.value[0].raw,t=await w.importExcel(a,"大仪平台");t.code===200?(o.success("大仪平台数据导入成功"),k(),await _()):o.error(t.message||"导入失败")}catch(a){a!=="cancel"&&(console.error("导入失败:",a),o.error("导入失败："+(a.message||"未知错误")))}finally{g.value=!1}},C=()=>{d.value=[]},k=()=>{r.value=[]},D=()=>{_(),o.success("统计信息已刷新")},G=()=>{b.push("/")},L=async()=>{try{await x.confirm("确定要退出登录吗？","确认退出",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await P.logout(),localStorage.removeItem("token"),o.success("已退出登录"),b.push("/login")}catch(a){a!=="cancel"&&console.error("退出登录失败:",a)}};return N(()=>{_()}),(a,t)=>{const i=u("el-button"),m=u("el-card"),c=u("el-col"),B=u("el-row"),z=u("el-icon"),E=u("el-upload");return V(),R("div",Q,[s("div",q,[t[1]||(t[1]=s("h1",{class:"title"},"管理员面板",-1)),l(i,{type:"danger",onClick:L},{default:e(()=>t[0]||(t[0]=[n("退出登录",-1)])),_:1,__:[0]})]),l(B,{gutter:20,class:"statistics-row"},{default:e(()=>[l(c,{span:8},{default:e(()=>[l(m,{class:"stat-card"},{default:e(()=>[s("div",H,[s("div",J,y(f.value.total),1),t[2]||(t[2]=s("div",{class:"stat-label"},"总设备数",-1))])]),_:1})]),_:1}),l(c,{span:8},{default:e(()=>[l(m,{class:"stat-card guozi"},{default:e(()=>[s("div",K,[s("div",O,y(f.value.guozi),1),t[3]||(t[3]=s("div",{class:"stat-label"},"国资系统",-1))])]),_:1})]),_:1}),l(c,{span:8},{default:e(()=>[l(m,{class:"stat-card dayi"},{default:e(()=>[s("div",W,[s("div",X,y(f.value.dayi),1),t[4]||(t[4]=s("div",{class:"stat-label"},"大仪平台",-1))])]),_:1})]),_:1})]),_:1}),l(m,{class:"import-card",shadow:"hover"},{header:e(()=>t[5]||(t[5]=[s("div",{class:"card-header"},[s("span",null,"Excel数据导入")],-1)])),default:e(()=>[l(B,{gutter:20},{default:e(()=>[l(c,{span:12},{default:e(()=>[s("div",Y,[t[10]||(t[10]=s("h3",null,"国资系统数据导入",-1)),t[11]||(t[11]=s("p",{class:"import-desc"},'导入"全校10万元以上设备清单"Excel文件',-1)),l(E,{ref:"guoziUploadRef",class:"upload-demo",drag:"","auto-upload":!1,limit:1,accept:".xlsx,.xls","on-change":U,"file-list":d.value},{tip:e(()=>t[6]||(t[6]=[s("div",{class:"el-upload__tip"}," 只能上传 xlsx/xls 文件 ",-1)])),default:e(()=>[l(z,{class:"el-icon--upload"},{default:e(()=>[l(T(F))]),_:1}),t[7]||(t[7]=s("div",{class:"el-upload__text"},[n(" 将文件拖到此处，或"),s("em",null,"点击上传")],-1))]),_:1,__:[7]},8,["file-list"]),s("div",Z,[l(i,{type:"primary",loading:v.value,disabled:d.value.length===0,onClick:h},{default:e(()=>t[8]||(t[8]=[n(" 导入国资系统数据 ",-1)])),_:1,__:[8]},8,["loading","disabled"]),l(i,{onClick:C},{default:e(()=>t[9]||(t[9]=[n("清空",-1)])),_:1,__:[9]})])])]),_:1}),l(c,{span:12},{default:e(()=>[s("div",$,[t[16]||(t[16]=s("h3",null,"大仪平台数据导入",-1)),t[17]||(t[17]=s("p",{class:"import-desc"},'导入"大型仪器开放共享平台数据"Excel文件',-1)),l(E,{ref:"dayiUploadRef",class:"upload-demo",drag:"","auto-upload":!1,limit:1,accept:".xlsx,.xls","on-change":S,"file-list":r.value},{tip:e(()=>t[12]||(t[12]=[s("div",{class:"el-upload__tip"}," 只能上传 xlsx/xls 文件 ",-1)])),default:e(()=>[l(z,{class:"el-icon--upload"},{default:e(()=>[l(T(F))]),_:1}),t[13]||(t[13]=s("div",{class:"el-upload__text"},[n(" 将文件拖到此处，或"),s("em",null,"点击上传")],-1))]),_:1,__:[13]},8,["file-list"]),s("div",tt,[l(i,{type:"success",loading:g.value,disabled:r.value.length===0,onClick:A},{default:e(()=>t[14]||(t[14]=[n(" 导入大仪平台数据 ",-1)])),_:1,__:[14]},8,["loading","disabled"]),l(i,{onClick:k},{default:e(()=>t[15]||(t[15]=[n("清空",-1)])),_:1,__:[15]})])])]),_:1})]),_:1})]),_:1}),s("div",st,[l(i,{type:"primary",onClick:G},{default:e(()=>t[18]||(t[18]=[n("返回查询页面",-1)])),_:1,__:[18]}),l(i,{onClick:D},{default:e(()=>t[19]||(t[19]=[n("刷新统计",-1)])),_:1,__:[19]})])])}}}),nt=j(et,[["__scopeId","data-v-dc47fb31"]]);export{nt as default};
