import{d as O,r as m,c as v,a as X,o as q,b as R,e as _,f as g,g as e,h as k,i as x,w as u,j as d,u as F,k as H,l as N,m as J,t as S,n as W,s as $}from"./index-DuZf145c.js";import{i as B,_ as ee}from"./_plugin-vue_export-helper-BUfy8ct9.js";const te=[{assetNumber:"GZ001",deviceName:"高效液相色谱仪",institution:"化学学院",department:"分析化学实验室",price:"45.8",brand:"Agilent",model:"1260 Infinity",status:"正常使用",location:"化学楼301",manufacturer:"安捷伦科技",purchaseDate:"2020-03-15",storageDate:"2020-04-01",usage2023:"1200",usage2024:"980",dataSource:"国资系统"},{assetNumber:"DY002",deviceName:"扫描电子显微镜",institution:"材料学院",department:"材料表征中心",price:"280.5",brand:"ZEISS",model:"Sigma 300",status:"正常使用",location:"材料楼B201",manufacturer:"蔡司公司",purchaseDate:"2019-08-20",storageDate:"2019-09-10",usage2023:"2400",usage2024:"2100",dataSource:"大仪平台"},{assetNumber:"GZ003",deviceName:"X射线衍射仪",institution:"物理学院",department:"凝聚态物理实验室",price:"120.0",brand:"Bruker",model:"D8 Advance",status:"正常使用",location:"物理楼405",manufacturer:"布鲁克公司",purchaseDate:"2021-06-10",storageDate:"2021-07-01",usage2023:"800",usage2024:"750",dataSource:"国资系统"},{assetNumber:"DY004",deviceName:"核磁共振波谱仪",institution:"化学学院",department:"有机化学实验室",price:"450.0",brand:"Bruker",model:"AVANCE NEO 400",status:"正常使用",location:"化学楼501",manufacturer:"布鲁克公司",purchaseDate:"2022-01-15",storageDate:"2022-02-20",usage2023:"1800",usage2024:"1650",dataSource:"大仪平台"},{assetNumber:"GZ005",deviceName:"气相色谱质谱联用仪",institution:"环境学院",department:"环境监测中心",price:"85.6",brand:"Agilent",model:"7890B-5977B",status:"正常使用",location:"环境楼203",manufacturer:"安捷伦科技",purchaseDate:"2020-11-05",storageDate:"2020-12-01",usage2023:"1500",usage2024:"1320",dataSource:"国资系统"}],ae=O("instrument",()=>{const D=m(te),l=m(!1),h=m([]),n=m(0),p=v(()=>{const t=new Set;return D.value.forEach(a=>{t.add(a.deviceName),t.add(a.model),t.add(a.brand)}),Array.from(t)});return{instruments:D,loading:l,searchResults:h,total:n,suggestions:p,searchInstruments:async t=>{l.value=!0;try{const a=await B.search(t);if(a.code===200){const i=a.data;return h.value=i.records||[],n.value=i.total||0,{data:i.records||[],total:i.total||0,pageSize:i.pageSize||t.pageSize,currentPage:i.currentPage||t.currentPage}}else throw new Error(a.message||"搜索失败")}catch(a){throw console.error("搜索失败:",a),h.value=[],n.value=0,a}finally{l.value=!1}},getSuggestions:async t=>{if(!t.trim())return[];try{const a=await B.getSuggestions(t);return a.code===200?a.data||[]:[]}catch(a){return console.error("获取建议失败:",a),[]}},setSearchResults:t=>{h.value=t},setTotal:t=>{n.value=t}}}),oe={class:"instrument-query-container"},se={class:"header"},re={class:"search-section"},le={class:"results-header"},ne={class:"result-count"},ue={key:0,class:"pagination-container"},ie=X({__name:"InstrumentQuery",setup(D){const l=ae(),h=F(),n=m(""),p=m(1),f=m(20);let b=null;const t=v(()=>l.loading),a=v(()=>l.searchResults),i=v(()=>l.total),T=v(()=>a.value.length>0&&!t.value),V=v(()=>n.value.trim()!==""&&a.value.length===0&&!t.value),A=async(c,s)=>{try{const C=(await l.getSuggestions(c)).map(w=>({value:w}));s(C)}catch(y){console.error("获取搜索建议失败:",y),s([])}},E=c=>{n.value=c.value,I()},P=c=>{if(b&&clearTimeout(b),!c.trim()){l.setSearchResults([]),l.setTotal(0);return}b=setTimeout(()=>{I()},500)},I=async()=>{if(!n.value.trim()){l.setSearchResults([]),l.setTotal(0);return}p.value=1,await z()},z=async()=>{await l.searchInstruments({keyword:n.value,pageSize:f.value,currentPage:p.value})},Z=c=>{f.value=c,p.value=1,n.value.trim()&&z()},G=c=>{p.value=c,n.value.trim()&&z()},U=()=>{h.push("/login")};return q(()=>{}),(c,s)=>{const y=d("el-button"),C=d("el-autocomplete"),w=d("el-card"),o=d("el-table-column"),j=d("el-tag"),Q=d("el-table"),Y=d("el-pagination"),K=d("el-icon"),L=d("el-empty"),M=H("loading");return _(),R("div",oe,[g("div",se,[s[4]||(s[4]=g("h1",{class:"title"},"设备仪器查询平台",-1)),e(y,{type:"text",onClick:U,class:"admin-link"},{default:u(()=>s[3]||(s[3]=[N(" 管理员登录 ",-1)])),_:1,__:[3]})]),e(w,{class:"search-card",shadow:"hover"},{default:u(()=>[g("div",re,[e(C,{modelValue:n.value,"onUpdate:modelValue":s[0]||(s[0]=r=>n.value=r),class:"search-input",placeholder:"请输入设备名称或型号进行查询...","fetch-suggestions":A,"trigger-on-focus":!1,clearable:"",size:"large",onSelect:E,onInput:P},null,8,["modelValue"])])]),_:1}),T.value?(_(),k(w,{key:0,class:"results-card",shadow:"hover"},{default:u(()=>[g("div",le,[g("span",ne,"共找到 "+S(i.value)+" 条记录",1)]),J((_(),k(Q,{data:a.value,stripe:"",border:"",size:"default",class:"results-table","header-cell-style":{backgroundColor:"#f8f9fa",color:"#555",fontSize:"15px",height:"50px"},"cell-style":{fontSize:"14px",height:"45px"},"empty-text":"暂无数据"},{default:u(()=>[e(o,{type:"index",label:"序号",width:"60",align:"center"}),e(o,{prop:"assetNumber",label:"资产编号",width:"120","show-overflow-tooltip":""}),e(o,{prop:"deviceName",label:"设备名称",width:"200","show-overflow-tooltip":""},{default:u(({row:r})=>[g("strong",null,S(r.deviceName),1)]),_:1}),e(o,{prop:"model",label:"型号",width:"140","show-overflow-tooltip":""}),e(o,{prop:"brand",label:"品牌",width:"100","show-overflow-tooltip":""}),e(o,{prop:"price",label:"单价（万元）",width:"110",align:"right","show-overflow-tooltip":""}),e(o,{prop:"manufacturer",label:"厂家",width:"120","show-overflow-tooltip":""}),e(o,{prop:"institution",label:"所属院校",width:"120","show-overflow-tooltip":""}),e(o,{prop:"department",label:"领用单位",width:"140","show-overflow-tooltip":""}),e(o,{prop:"location",label:"存放地",width:"120","show-overflow-tooltip":""}),e(o,{prop:"status",label:"使用现状",width:"100","show-overflow-tooltip":""}),e(o,{prop:"purchaseDate",label:"购置日期",width:"110","show-overflow-tooltip":""}),e(o,{prop:"storageDate",label:"入库日期",width:"110","show-overflow-tooltip":""}),e(o,{prop:"usage2023",label:"2023年使用机时（小时）",width:"120",align:"center","show-overflow-tooltip":""},{default:u(({row:r})=>[N(S(r.usage2023||"-"),1)]),_:1}),e(o,{prop:"usage2024",label:"2024年使用机时（小时）",width:"120",align:"center","show-overflow-tooltip":""},{default:u(({row:r})=>[N(S(r.usage2024||"-"),1)]),_:1}),e(o,{prop:"dataSource",label:"数据来源",width:"100",align:"center","show-overflow-tooltip":""},{default:u(({row:r})=>[e(j,{type:r.dataSource==="国资系统"?"primary":"success",size:"small"},{default:u(()=>[N(S(r.dataSource),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])),[[M,t.value]]),i.value>0?(_(),R("div",ue,[e(Y,{"current-page":p.value,"onUpdate:currentPage":s[1]||(s[1]=r=>p.value=r),"page-size":f.value,"onUpdate:pageSize":s[2]||(s[2]=r=>f.value=r),"page-sizes":[10,20,50,100],total:i.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Z,onCurrentChange:G},null,8,["current-page","page-size","total"])])):x("",!0)]),_:1})):x("",!0),V.value?(_(),k(w,{key:1,class:"no-results-card",shadow:"hover"},{default:u(()=>[e(L,{description:"未找到相关设备信息"},{image:u(()=>[e(K,{size:"64",color:"#c0c4cc"},{default:u(()=>[e(W($))]),_:1})]),default:u(()=>[s[5]||(s[5]=g("p",{class:"no-results-tip"},"请尝试使用设备名称、型号或品牌进行搜索",-1))]),_:1,__:[5]})]),_:1})):x("",!0)])}}}),pe=ee(ie,[["__scopeId","data-v-99ec3c8e"]]);export{pe as default};
