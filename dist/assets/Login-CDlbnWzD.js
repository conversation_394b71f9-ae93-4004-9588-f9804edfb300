import{a as x,r as v,p as V,b as k,e as h,f as o,g as a,w as l,q as C,j as n,l as i,u as L,v as R,E as t}from"./index-DuZf145c.js";import{a as q,_ as z}from"./_plugin-vue_export-helper-BUfy8ct9.js";const B={class:"login-container"},E={class:"login-card"},F={class:"login-footer"},N=x({__name:"Login",setup(U){const f=L(),u=v(),r=V({username:"",password:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:2,max:20,message:"用户名长度在 2 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}]},d=v(!1),m=async()=>{var p,e;if(u.value)try{await u.value.validate(),d.value=!0;const s=await q.login(r.username,r.password);s.code===200?(localStorage.setItem("token",s.data),t.success("登录成功"),f.push("/admin")):t.error(s.message||"登录失败")}catch(s){console.error("登录失败:",s),(e=(p=s.response)==null?void 0:p.data)!=null&&e.message?t.error(s.response.data.message):s.message?t.error(s.message):t.error("登录失败，请检查网络连接")}finally{d.value=!1}},b=()=>{f.push("/")};return(p,e)=>{const s=n("el-input"),g=n("el-form-item"),_=n("el-button"),y=n("el-form");return h(),k("div",B,[o("div",E,[e[5]||(e[5]=o("div",{class:"login-header"},[o("h1",{class:"login-title"},"设备仪器查询系统"),o("p",{class:"login-subtitle"},"管理员登录")],-1)),a(y,{ref_key:"loginFormRef",ref:u,model:r,rules:w,class:"login-form",onSubmit:C(m,["prevent"])},{default:l(()=>[a(g,{prop:"username"},{default:l(()=>[a(s,{modelValue:r.username,"onUpdate:modelValue":e[0]||(e[0]=c=>r.username=c),placeholder:"请输入用户名",size:"large","prefix-icon":"User",clearable:""},null,8,["modelValue"])]),_:1}),a(g,{prop:"password"},{default:l(()=>[a(s,{modelValue:r.password,"onUpdate:modelValue":e[1]||(e[1]=c=>r.password=c),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":"",clearable:"",onKeyup:R(m,["enter"])},null,8,["modelValue"])]),_:1}),a(g,null,{default:l(()=>[a(_,{type:"primary",size:"large",loading:d.value,class:"login-button",onClick:m},{default:l(()=>e[2]||(e[2]=[i(" 登录 ",-1)])),_:1,__:[2]},8,["loading"])]),_:1})]),_:1},8,["model"]),o("div",F,[e[4]||(e[4]=o("p",{class:"default-account"},[i(" 默认管理员账号："),o("strong",null,"admin"),i(" / "),o("strong",null,"admin123")],-1)),a(_,{type:"text",onClick:b},{default:l(()=>e[3]||(e[3]=[i(" 返回查询页面 ",-1)])),_:1,__:[3]})])])])}}}),M=z(N,[["__scopeId","data-v-b959c5d8"]]);export{M as default};
