<template>
  <div class="instrument-query-container">
    <!-- 页面标题 -->
    <div class="header">
      <h1 class="title">设备仪器查询平台</h1>
      <el-button type="text" @click="goToLogin" class="admin-link">
        管理员登录
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="hover">
      <div class="search-section">
        <el-autocomplete
          v-model="searchKeyword"
          class="search-input"
          placeholder="请输入设备名称或型号进行查询..."
          :fetch-suggestions="handleSuggestions"
          :trigger-on-focus="false"
          clearable
          size="large"
          @select="handleSuggestionSelect"
          @input="handleInputChange"
        />
      </div>
    </el-card>

    <!-- 结果区域 -->
    <el-card v-if="showResults" class="results-card" shadow="hover">
      <!-- 结果统计 -->
      <div class="results-header">
        <span class="result-count">共找到 {{ total }} 条记录</span>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="searchResults"
        stripe
        border
        size="default"
        class="results-table"
        :header-cell-style="{ backgroundColor: '#f8f9fa', color: '#555', fontSize: '15px', height: '50px' }"
        :cell-style="{ fontSize: '14px', height: '45px' }"
        empty-text="暂无数据"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        
        <el-table-column prop="assetNumber" label="资产编号" width="120" show-overflow-tooltip />
        
        <el-table-column prop="deviceName" label="设备名称" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <strong>{{ row.deviceName }}</strong>
          </template>
        </el-table-column>
        
        <el-table-column prop="model" label="型号" width="140" show-overflow-tooltip />
        
        <el-table-column prop="brand" label="品牌" width="100" show-overflow-tooltip />
        
        <el-table-column prop="price" label="单价（万元）" width="110" align="right" show-overflow-tooltip />
        
        <el-table-column prop="manufacturer" label="厂家" width="120" show-overflow-tooltip />
        
        <el-table-column prop="institution" label="所属院校" width="120" show-overflow-tooltip />
        
        <el-table-column prop="department" label="领用单位" width="140" show-overflow-tooltip />
        
        <el-table-column prop="location" label="存放地" width="120" show-overflow-tooltip />
        
        <el-table-column prop="status" label="使用现状" width="100" show-overflow-tooltip />
        
        <el-table-column prop="purchaseDate" label="购置日期" width="110" show-overflow-tooltip />
        
        <el-table-column prop="storageDate" label="入库日期" width="110" show-overflow-tooltip />
        
        <el-table-column prop="usage2023" label="2023年使用机时（小时）" width="120" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.usage2023 || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="usage2024" label="2024年使用机时（小时）" width="120" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.usage2024 || '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="dataSource" label="数据来源" width="100" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <el-tag
              :type="row.dataSource === '国资系统' ? 'primary' : 'success'"
              size="small"
            >
              {{ row.dataSource }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="total > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 无结果提示 -->
    <el-card v-if="showNoResults" class="no-results-card" shadow="hover">
      <el-empty description="未找到相关设备信息">
        <template #image>
          <el-icon size="64" color="#c0c4cc">
            <Search />
          </el-icon>
        </template>
        <p class="no-results-tip">请尝试使用设备名称、型号或品牌进行搜索</p>
      </el-empty>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import { useInstrumentStore } from '@/stores/instrument'
import type { InstrumentData } from '@/types/instrument'

// 使用store和router
const instrumentStore = useInstrumentStore()
const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
let searchTimer: NodeJS.Timeout | null = null

// 计算属性
const loading = computed(() => instrumentStore.loading)
const searchResults = computed(() => instrumentStore.searchResults)
const total = computed(() => instrumentStore.total)

const showResults = computed(() => {
  return searchResults.value.length > 0 && !loading.value
})

const showNoResults = computed(() => {
  return searchKeyword.value.trim() !== '' && 
         searchResults.value.length === 0 && 
         !loading.value
})

// 搜索建议处理
const handleSuggestions = async (queryString: string, callback: (suggestions: any[]) => void) => {
  try {
    const suggestions = await instrumentStore.getSuggestions(queryString)
    const formattedSuggestions = suggestions.map(item => ({ value: item }))
    callback(formattedSuggestions)
  } catch (error) {
    console.error('获取搜索建议失败:', error)
    callback([])
  }
}

// 选择建议项
const handleSuggestionSelect = (item: { value: string }) => {
  searchKeyword.value = item.value
  // 选择建议后立即搜索
  handleSearch()
}

// 输入变化处理（防抖）
const handleInputChange = (value: string) => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  
  // 如果输入为空，清空结果
  if (!value.trim()) {
    instrumentStore.setSearchResults([])
    instrumentStore.setTotal(0)
    return
  }
  
  // 设置新的定时器，300ms后执行搜索（优化响应速度）
  searchTimer = setTimeout(() => {
    handleSearch()
  }, 300)
}

// 执行搜索
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    instrumentStore.setSearchResults([])
    instrumentStore.setTotal(0)
    return
  }
  
  currentPage.value = 1
  await performSearch()
}

// 执行搜索逻辑
const performSearch = async () => {
  await instrumentStore.searchInstruments({
    keyword: searchKeyword.value,
    pageSize: pageSize.value,
    currentPage: currentPage.value
  })
}

// 分页大小改变
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
  if (searchKeyword.value.trim()) {
    performSearch()
  }
}

// 当前页改变
const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  if (searchKeyword.value.trim()) {
    performSearch()
  }
}

// 跳转到管理员登录
const goToLogin = () => {
  router.push('/login')
}

// 组件挂载
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
.instrument-query-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.title {
  color: #2c5aa0;
  font-size: 28px;
  font-weight: 600;
  margin: 0;
}

.admin-link {
  color: #666;
  font-size: 14px;
}

.admin-link:hover {
  color: #2c5aa0;
}

.search-card {
  margin-bottom: 25px;
}

.search-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  flex: 1;
}

.search-input :deep(.el-input__inner) {
  font-size: 16px;
}

.results-card {
  margin-bottom: 20px;
}

.results-header {
  margin-bottom: 16px;
  padding: 0 4px;
}

.result-count {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.results-table {
  width: 100%;
  font-size: 14px;
}

.results-table :deep(.el-table__header-wrapper) {
  position: sticky;
  top: 0;
  z-index: 10;
}

.results-table :deep(.el-table__header) {
  font-size: 15px;
  font-weight: 600;
}

.results-table :deep(.el-table__body) {
  font-size: 14px;
}

.results-table :deep(.el-table__row) {
  height: 45px;
}

.results-table :deep(.el-table__header-wrapper .el-table__header .has-gutter tr th) {
  height: 50px;
  padding: 12px 0;
}

.results-table :deep(.el-table__body-wrapper .el-table__body tbody tr td) {
  height: 45px;
  padding: 12px 8px;
  line-height: 1.5;
}

.results-table :deep(.el-table__body-wrapper .el-table__body tbody tr:hover td) {
  background-color: #f5f7fa;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.no-results-card {
  text-align: center;
  padding: 40px 20px;
}

.no-results-tip {
  color: #999;
  font-size: 14px;
  margin-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .instrument-query-container {
    padding: 10px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .search-section {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-input {
    width: 100%;
  }
  
  .results-table {
    font-size: 12px;
  }
}
</style>