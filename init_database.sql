-- 设备仪器查询系统数据库初始化脚本
-- 请在MySQL中执行此脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS yqcx CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE yqcx;

-- 创建仪器设备表
DROP TABLE IF EXISTS instrument;
CREATE TABLE instrument (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    asset_number VARCHAR(100) COMMENT '资产编号',
    device_name VARCHAR(200) NOT NULL COMMENT '设备名称',
    institution VARCHAR(200) COMMENT '所属院校',
    department VARCHAR(200) COMMENT '领用单位',
    price DECIMAL(10,2) COMMENT '单价（万元）',
    brand VARCHAR(100) COMMENT '品牌',
    model VARCHAR(200) COMMENT '型号',
    status VARCHAR(50) COMMENT '使用现状',
    location VARCHAR(200) COMMENT '存放地',
    manufacturer VARCHAR(200) COMMENT '厂家',
    purchase_date VARCHAR(50) COMMENT '购置日期',
    storage_date VARCHAR(50) COMMENT '入库日期',
    usage_2023 INT COMMENT '2023年使用机时（小时）',
    usage_2024 INT COMMENT '2024年使用机时（小时）',
    data_source VARCHAR(20) NOT NULL COMMENT '数据来源（国资系统/大仪平台）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0-未删除，1-已删除）',
    
    INDEX idx_device_name (device_name),
    INDEX idx_model (model),
    INDEX idx_brand (brand),
    INDEX idx_data_source (data_source),
    INDEX idx_create_time (create_time),
    INDEX idx_deleted (deleted),
    INDEX idx_search_composite (deleted, device_name, model, brand),
    FULLTEXT INDEX idx_fulltext_search (device_name, model, brand, manufacturer, asset_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='仪器设备表';

-- 创建用户表
DROP TABLE IF EXISTS user;
CREATE TABLE user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    real_name VARCHAR(100) COMMENT '真实姓名',
    role VARCHAR(20) NOT NULL DEFAULT 'USER' COMMENT '角色（ADMIN-管理员，USER-普通用户）',
    status TINYINT DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0-未删除，1-已删除）',
    
    INDEX idx_username (username),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 插入默认管理员用户（用户名：admin，密码：admin123）
-- 密码已使用BCrypt加密：$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfcqO9p4MJnGMHWeiUU.2Z/u
INSERT INTO user (username, password, real_name, role, status) 
VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfcqO9p4MJnGMHWeiUU.2Z/u', '系统管理员', 'ADMIN', 1);

-- 插入示例仪器设备数据
INSERT INTO instrument (
    asset_number, device_name, institution, department, price, brand, model, 
    status, location, manufacturer, purchase_date, storage_date, 
    usage_2023, usage_2024, data_source
) VALUES 
('GZ001', '高效液相色谱仪', '清华大学', '化学系', 45.8, 'Agilent', '1260 Infinity', 
 '正常使用', '化学楼301', 'Agilent Technologies', '2020-03-15', '2020-04-01', 
 1200, 1350, '国资系统'),
 
('GZ002', '扫描电子显微镜', '北京大学', '材料学院', 120.5, 'ZEISS', 'Sigma 300', 
 '正常使用', '材料楼B201', 'Carl Zeiss', '2019-08-20', '2019-09-10', 
 800, 950, '国资系统'),
 
('GZ003', 'X射线衍射仪', '中科院', '物理所', 85.2, 'Bruker', 'D8 Advance', 
 '正常使用', '实验楼A305', 'Bruker AXS', '2021-01-10', '2021-02-01', 
 1500, 1680, '国资系统'),
 
('GZ004', '原子力显微镜', '复旦大学', '物理系', 95.6, 'Bruker', 'Dimension Icon', 
 '正常使用', '纳米中心', 'Bruker Nano Surfaces', '2020-06-15', '2020-07-01', 
 900, 1100, '国资系统'),
 
('GZ005', '傅里叶变换红外光谱仪', '上海交大', '化学系', 28.9, 'Thermo', 'Nicolet iS50', 
 '正常使用', '分析测试中心', 'Thermo Fisher Scientific', '2021-03-20', '2021-04-10', 
 1800, 2000, '国资系统'),

('DY001', '核磁共振波谱仪', '北京理工大学', '化学学院', 200.0, 'Bruker', 'AVANCE III 400', 
 '正常使用', '分析中心', 'Bruker BioSpin', '2018-12-05', '2019-01-15', 
 2200, 2400, '大仪平台'),
 
('DY002', '气相色谱质谱联用仪', '华中科技大学', '环境学院', 68.9, 'Thermo', 'TSQ 8000 Evo', 
 '正常使用', '环境楼205', 'Thermo Fisher Scientific', '2020-11-20', '2020-12-10', 
 1100, 1250, '大仪平台'),
 
('DY003', '液相色谱串联质谱仪', '西安交大', '医学院', 150.3, 'Waters', 'ACQUITY UPLC', 
 '正常使用', '医学楼B302', 'Waters Corporation', '2019-09-10', '2019-10-01', 
 1600, 1800, '大仪平台'),
 
('DY004', '透射电子显微镜', '中南大学', '材料学院', 280.5, 'FEI', 'Tecnai G2 F20', 
 '正常使用', '电镜中心', 'FEI Company', '2017-05-20', '2017-06-15', 
 1000, 1200, '大仪平台'),
 
('DY005', '拉曼光谱仪', '大连理工大学', '化工学院', 45.2, 'Horiba', 'LabRAM HR Evolution', 
 '正常使用', '光谱实验室', 'Horiba Scientific', '2020-08-15', '2020-09-01', 
 1400, 1550, '大仪平台');

-- 查看插入结果
SELECT '数据库初始化完成！' as message;
SELECT COUNT(*) as '仪器设备总数' FROM instrument;
SELECT data_source as '数据来源', COUNT(*) as '数量' FROM instrument GROUP BY data_source;
SELECT COUNT(*) as '用户总数' FROM user;

COMMIT;