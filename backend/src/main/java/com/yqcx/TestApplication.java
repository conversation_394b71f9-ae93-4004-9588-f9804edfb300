package com.yqcx;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@SpringBootApplication
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class TestApplication {

    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
        System.out.println("=== 测试服务器启动成功 ===");
        System.out.println("API地址: http://localhost:8080/api");
    }

    @PostMapping("/auth/login")
    public Map<String, Object> login(@RequestBody Map<String, String> loginData) {
        Map<String, Object> response = new HashMap<>();
        
        String username = loginData.get("username");
        String password = loginData.get("password");
        
        // 简单验证
        if ("admin".equals(username) && "admin123".equals(password)) {
            response.put("code", 200);
            response.put("message", "登录成功");
            response.put("data", "test-jwt-token-12345");
        } else {
            response.put("code", 401);
            response.put("message", "用户名或密码错误");
            response.put("data", null);
        }
        
        return response;
    }

    @GetMapping("/instruments/search")
    public Map<String, Object> searchInstruments(
            @RequestParam(defaultValue = "") String keyword,
            @RequestParam(defaultValue = "") String dataSource,
            @RequestParam(defaultValue = "1") int currentPage,
            @RequestParam(defaultValue = "10") int pageSize) {
        
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> data = new HashMap<>();
        
        // 模拟数据
        data.put("records", java.util.Arrays.asList(
            createMockInstrument("高效液相色谱仪", "Agilent 1260", "国资系统"),
            createMockInstrument("扫描电子显微镜", "ZEISS Sigma 300", "大仪平台")
        ));
        data.put("total", 2);
        data.put("currentPage", currentPage);
        data.put("pageSize", pageSize);
        
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", data);
        
        return response;
    }

    @GetMapping("/instruments/suggestions")
    public Map<String, Object> getSuggestions(@RequestParam String keyword) {
        Map<String, Object> response = new HashMap<>();
        
        java.util.List<String> suggestions = java.util.Arrays.asList(
            "高效液相色谱仪",
            "扫描电子显微镜",
            "核磁共振仪",
            "HPLC",
            "Agilent 1260"
        );
        
        response.put("code", 200);
        response.put("message", "获取建议成功");
        response.put("data", suggestions);
        
        return response;
    }

    private Map<String, Object> createMockInstrument(String name, String model, String dataSource) {
        Map<String, Object> instrument = new HashMap<>();
        instrument.put("id", 1);
        instrument.put("deviceName", name);
        instrument.put("model", model);
        instrument.put("brand", "测试品牌");
        instrument.put("institution", "测试大学");
        instrument.put("department", "测试学院");
        instrument.put("price", 50.0);
        instrument.put("status", "正常");
        instrument.put("location", "实验楼101");
        instrument.put("manufacturer", "测试厂家");
        instrument.put("dataSource", dataSource);
        return instrument;
    }
}