package com.yqcx;

import com.yqcx.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 仪器查询系统启动类
 */
@Slf4j
@SpringBootApplication
@MapperScan("com.yqcx.mapper")
@RequiredArgsConstructor
public class InstrumentQuerySystemApplication implements CommandLineRunner {

    private final UserService userService;

    public static void main(String[] args) {
        SpringApplication.run(InstrumentQuerySystemApplication.class, args);
        log.info("=== 仪器查询系统启动成功 ===");
        log.info("API文档地址: http://localhost:8080/api");
        log.info("默认管理员账号: admin / admin123");
    }

    @Override
    public void run(String... args) throws Exception {
        // 创建默认管理员用户
        userService.createDefaultAdmin();
    }
}