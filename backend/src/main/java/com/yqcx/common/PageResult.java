package com.yqcx.common;

import lombok.Data;

import java.util.List;

/**
 * 分页响应结果类
 */
@Data
public class PageResult<T> {

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Long currentPage;

    /**
     * 每页大小
     */
    private Long pageSize;

    /**
     * 总页数
     */
    private Long totalPages;

    public PageResult() {}

    public PageResult(List<T> records, Long total, Long currentPage, Long pageSize) {
        this.records = records;
        this.total = total;
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.totalPages = (total + pageSize - 1) / pageSize;
    }

    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(List<T> records, Long total, Long currentPage, Long pageSize) {
        return new PageResult<>(records, total, currentPage, pageSize);
    }
}