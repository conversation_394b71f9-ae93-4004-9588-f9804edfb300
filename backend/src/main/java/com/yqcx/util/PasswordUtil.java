package com.yqcx.util;

import java.util.regex.Pattern;

/**
 * 密码工具类
 */
public class PasswordUtil {

    // 密码强度正则：至少8位，包含大小写字母、数字
    private static final Pattern PASSWORD_PATTERN = Pattern.compile(
        "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$"
    );

    /**
     * 验证密码强度
     * @param password 密码
     * @return 是否符合强度要求
     */
    public static boolean isValidPassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return false;
        }
        return PASSWORD_PATTERN.matcher(password).matches();
    }

    /**
     * 获取密码强度要求说明
     * @return 密码要求说明
     */
    public static String getPasswordRequirement() {
        return "密码至少8位，必须包含大写字母、小写字母和数字";
    }

    /**
     * 检查密码是否包含常见弱密码
     * @param password 密码
     * @return 是否为弱密码
     */
    public static boolean isWeakPassword(String password) {
        if (password == null) return true;
        
        String[] weakPasswords = {
            "password", "123456", "123456789", "qwerty", "abc123",
            "password123", "admin", "admin123", "root", "user"
        };
        
        String lowerPassword = password.toLowerCase();
        for (String weak : weakPasswords) {
            if (lowerPassword.contains(weak)) {
                return true;
            }
        }
        return false;
    }
}
