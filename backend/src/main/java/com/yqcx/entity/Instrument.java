package com.yqcx.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 仪器设备实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "instrument")
@TableName("instrument")
public class Instrument {

    /**
     * 主键ID
     */
    @Id
    @TableId(value = "id", type = IdType.AUTO)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 资产编号
     */
    @Column(name = "asset_number", length = 100)
    private String assetNumber;

    /**
     * 设备名称（仪器名称）
     */
    @NotBlank(message = "设备名称不能为空")
    @Column(name = "device_name", length = 200, nullable = false)
    private String deviceName;

    /**
     * 所属院校（所属机构）
     */
    @Column(name = "institution", length = 200)
    private String institution;

    /**
     * 领用单位
     */
    @Column(name = "department", length = 200)
    private String department;

    /**
     * 单价（万元）
     */
    @Column(name = "price", precision = 10, scale = 2)
    private Double price;

    /**
     * 品牌
     */
    @Column(name = "brand", length = 100)
    private String brand;

    /**
     * 型号
     */
    @Column(name = "model", length = 200)
    private String model;

    /**
     * 使用现状
     */
    @Column(name = "status", length = 50)
    private String status;

    /**
     * 存放地
     */
    @Column(name = "location", length = 200)
    private String location;

    /**
     * 厂家
     */
    @Column(name = "manufacturer", length = 200)
    private String manufacturer;

    /**
     * 购置日期
     */
    @Column(name = "purchase_date", length = 20)
    private String purchaseDate;

    /**
     * 入库日期
     */
    @Column(name = "storage_date", length = 20)
    private String storageDate;

    /**
     * 2023年使用机时（小时）
     */
    @Column(name = "usage_2023")
    private Integer usage2023;

    /**
     * 2024年使用机时（小时）
     */
    @Column(name = "usage_2024")
    private Integer usage2024;

    /**
     * 数据来源（国资系统/大仪平台）
     */
    @NotBlank(message = "数据来源不能为空")
    @Column(name = "data_source", length = 20, nullable = false)
    private String dataSource;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @Column(name = "deleted")
    private Integer deleted;
}