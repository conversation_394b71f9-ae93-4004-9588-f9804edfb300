package com.yqcx.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yqcx.common.PageResult;
import com.yqcx.dto.InstrumentSearchDTO;
import com.yqcx.entity.Instrument;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 仪器设备服务接口
 */
public interface InstrumentService extends IService<Instrument> {

    /**
     * 搜索仪器设备（分页）
     */
    PageResult<Instrument> searchInstruments(InstrumentSearchDTO searchDTO);

    /**
     * 获取搜索建议
     */
    List<String> getSuggestions(String keyword);

    /**
     * 导入Excel数据
     */
    void importExcelData(MultipartFile file, String dataSource);

    /**
     * 批量删除仪器设备
     */
    void batchDelete(List<Long> ids);

    /**
     * 根据数据来源统计数量
     */
    Long countByDataSource(String dataSource);
}