package com.yqcx.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yqcx.common.PageResult;
import com.yqcx.dto.InstrumentSearchDTO;
import com.yqcx.entity.Instrument;
import com.yqcx.mapper.InstrumentMapper;
import com.yqcx.service.InstrumentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仪器设备服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InstrumentServiceImpl extends ServiceImpl<InstrumentMapper, Instrument> implements InstrumentService {

    @Override
    public PageResult<Instrument> searchInstruments(InstrumentSearchDTO searchDTO) {
        Page<Instrument> page = new Page<>(searchDTO.getCurrentPage(), searchDTO.getPageSize());
        
        // 执行搜索
        Page<Instrument> resultPage = baseMapper.searchInstruments(page, searchDTO.getKeyword(), searchDTO.getDataSource());
        
        return PageResult.of(
            resultPage.getRecords(),
            resultPage.getTotal(),
            resultPage.getCurrent(),
            resultPage.getSize()
        );
    }

    @Override
    @Cacheable(value = "suggestions", key = "#keyword", unless = "#result.isEmpty()")
    public List<String> getSuggestions(String keyword) {
        Set<String> suggestions = new HashSet<>();

        // 获取设备名称建议
        List<String> deviceNames = baseMapper.getDeviceNameSuggestions(keyword);
        suggestions.addAll(deviceNames);

        // 获取型号建议
        List<String> models = baseMapper.getModelSuggestions(keyword);
        suggestions.addAll(models);

        // 获取品牌建议
        List<String> brands = baseMapper.getBrandSuggestions(keyword);
        suggestions.addAll(brands);

        return suggestions.stream()
                .filter(s -> s != null && !s.trim().isEmpty())
                .sorted()
                .limit(10)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"suggestions", "statistics"}, allEntries = true)
    public void importExcelData(MultipartFile file, String dataSource) {
        try {
            log.info("开始导入Excel数据，数据来源：{}", dataSource);
            
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            
            List<Instrument> instruments = new ArrayList<>();
            
            // 跳过表头，从第二行开始读取
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                Instrument instrument = parseRowToInstrument(row, dataSource);
                if (instrument != null) {
                    instruments.add(instrument);
                }
            }
            
            // 批量保存
            if (!instruments.isEmpty()) {
                this.saveBatch(instruments);
                log.info("成功导入{}条数据", instruments.size());
            }
            
            workbook.close();
            
        } catch (IOException e) {
            log.error("导入Excel数据失败", e);
            throw new RuntimeException("导入Excel数据失败：" + e.getMessage());
        }
    }

    /**
     * 解析Excel行数据为仪器对象
     */
    private Instrument parseRowToInstrument(Row row, String dataSource) {
        try {
            Instrument instrument = new Instrument();
            
            // 根据不同的数据源，字段顺序可能不同，这里需要根据实际Excel结构调整
            instrument.setAssetNumber(getCellStringValue(row.getCell(0)));
            instrument.setDeviceName(getCellStringValue(row.getCell(1)));
            instrument.setInstitution(getCellStringValue(row.getCell(2)));
            instrument.setDepartment(getCellStringValue(row.getCell(3)));
            
            // 单价处理
            String priceStr = getCellStringValue(row.getCell(4));
            if (priceStr != null && !priceStr.trim().isEmpty()) {
                try {
                    instrument.setPrice(Double.parseDouble(priceStr));
                } catch (NumberFormatException e) {
                    log.warn("价格格式错误：{}", priceStr);
                }
            }
            
            instrument.setBrand(getCellStringValue(row.getCell(5)));
            instrument.setModel(getCellStringValue(row.getCell(6)));
            instrument.setStatus(getCellStringValue(row.getCell(7)));
            instrument.setLocation(getCellStringValue(row.getCell(8)));
            instrument.setManufacturer(getCellStringValue(row.getCell(9)));
            instrument.setPurchaseDate(getCellStringValue(row.getCell(10)));
            instrument.setStorageDate(getCellStringValue(row.getCell(11)));
            
            // 使用机时处理
            String usage2023Str = getCellStringValue(row.getCell(12));
            if (usage2023Str != null && !usage2023Str.trim().isEmpty()) {
                try {
                    instrument.setUsage2023(Integer.parseInt(usage2023Str));
                } catch (NumberFormatException e) {
                    log.warn("2023年使用机时格式错误：{}", usage2023Str);
                }
            }
            
            String usage2024Str = getCellStringValue(row.getCell(13));
            if (usage2024Str != null && !usage2024Str.trim().isEmpty()) {
                try {
                    instrument.setUsage2024(Integer.parseInt(usage2024Str));
                } catch (NumberFormatException e) {
                    log.warn("2024年使用机时格式错误：{}", usage2024Str);
                }
            }
            
            instrument.setDataSource(dataSource);
            instrument.setCreateTime(LocalDateTime.now());
            instrument.setUpdateTime(LocalDateTime.now());
            
            // 验证必填字段
            if (instrument.getDeviceName() == null || instrument.getDeviceName().trim().isEmpty()) {
                log.warn("设备名称为空，跳过该行数据");
                return null;
            }
            
            return instrument;
            
        } catch (Exception e) {
            log.error("解析Excel行数据失败", e);
            return null;
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            this.removeByIds(ids);
            log.info("批量删除{}条仪器设备数据", ids.size());
        }
    }

    @Override
    @Cacheable(value = "statistics", key = "#dataSource")
    public Long countByDataSource(String dataSource) {
        QueryWrapper<Instrument> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_source", dataSource);
        return this.count(queryWrapper);
    }
}