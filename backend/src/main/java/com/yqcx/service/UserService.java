package com.yqcx.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yqcx.dto.LoginDTO;
import com.yqcx.entity.User;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {

    /**
     * 用户登录
     */
    String login(LoginDTO loginDTO);

    /**
     * 根据用户名查询用户
     */
    User findByUsername(String username);

    /**
     * 创建默认管理员用户
     */
    void createDefaultAdmin();
}