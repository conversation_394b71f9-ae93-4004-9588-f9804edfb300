package com.yqcx.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yqcx.entity.Instrument;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 仪器设备Mapper接口
 */
@Mapper
public interface InstrumentMapper extends BaseMapper<Instrument> {

    /**
     * 模糊搜索仪器设备（分页）
     */
    @Select("<script>" +
            "SELECT * FROM instrument WHERE deleted = 0 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (" +
            "device_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR model LIKE CONCAT('%', #{keyword}, '%') " +
            "OR brand LIKE CONCAT('%', #{keyword}, '%') " +
            "OR manufacturer LIKE CONCAT('%', #{keyword}, '%') " +
            "OR asset_number LIKE CONCAT('%', #{keyword}, '%')" +
            ")" +
            "</if>" +
            "<if test='dataSource != null and dataSource != \"\"'>" +
            "AND data_source = #{dataSource}" +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    IPage<Instrument> searchInstruments(Page<Instrument> page, 
                                       @Param("keyword") String keyword, 
                                       @Param("dataSource") String dataSource);

    /**
     * 获取搜索建议（设备名称）
     */
    @Select("<script>" +
            "SELECT DISTINCT device_name FROM instrument WHERE deleted = 0 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND device_name LIKE CONCAT('%', #{keyword}, '%')" +
            "</if>" +
            "ORDER BY device_name LIMIT 10" +
            "</script>")
    List<String> getDeviceNameSuggestions(@Param("keyword") String keyword);

    /**
     * 获取搜索建议（型号）
     */
    @Select("<script>" +
            "SELECT DISTINCT model FROM instrument WHERE deleted = 0 AND model IS NOT NULL AND model != '' " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND model LIKE CONCAT('%', #{keyword}, '%')" +
            "</if>" +
            "ORDER BY model LIMIT 10" +
            "</script>")
    List<String> getModelSuggestions(@Param("keyword") String keyword);

    /**
     * 获取搜索建议（品牌）
     */
    @Select("<script>" +
            "SELECT DISTINCT brand FROM instrument WHERE deleted = 0 AND brand IS NOT NULL AND brand != '' " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND brand LIKE CONCAT('%', #{keyword}, '%')" +
            "</if>" +
            "ORDER BY brand LIMIT 10" +
            "</script>")
    List<String> getBrandSuggestions(@Param("keyword") String keyword);
}