package com.yqcx.controller;

import com.yqcx.common.PageResult;
import com.yqcx.common.Result;
import com.yqcx.dto.InstrumentSearchDTO;
import com.yqcx.entity.Instrument;
import com.yqcx.service.InstrumentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 仪器设备控制器
 */
@Slf4j
@RestController
@RequestMapping("/instruments")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class InstrumentController {

    private final InstrumentService instrumentService;

    /**
     * 搜索仪器设备
     */
    @PostMapping("/search")
    public Result<PageResult<Instrument>> searchInstruments(@Valid @RequestBody InstrumentSearchDTO searchDTO) {
        try {
            PageResult<Instrument> result = instrumentService.searchInstruments(searchDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("搜索仪器设备失败", e);
            return Result.error("搜索失败：" + e.getMessage());
        }
    }

    /**
     * 获取搜索建议
     */
    @GetMapping("/suggestions")
    public Result<List<String>> getSuggestions(@RequestParam(required = false) String keyword) {
        try {
            List<String> suggestions = instrumentService.getSuggestions(keyword);
            return Result.success(suggestions);
        } catch (Exception e) {
            log.error("获取搜索建议失败", e);
            return Result.error("获取建议失败：" + e.getMessage());
        }
    }

    /**
     * 导入Excel数据（管理员权限）
     */
    @PostMapping("/import")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> importExcelData(@RequestParam("file") MultipartFile file,
                                       @RequestParam("dataSource") String dataSource) {
        try {
            if (file.isEmpty()) {
                return Result.error("请选择要导入的文件");
            }

            if (!"国资系统".equals(dataSource) && !"大仪平台".equals(dataSource)) {
                return Result.error("数据来源参数错误");
            }

            instrumentService.importExcelData(file, dataSource);
            return Result.success("数据导入成功");
        } catch (Exception e) {
            log.error("导入Excel数据失败", e);
            return Result.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 获取仪器设备详情
     */
    @GetMapping("/{id}")
    public Result<Instrument> getInstrumentById(@PathVariable Long id) {
        try {
            Instrument instrument = instrumentService.getById(id);
            if (instrument == null) {
                return Result.error("仪器设备不存在");
            }
            return Result.success(instrument);
        } catch (Exception e) {
            log.error("获取仪器设备详情失败", e);
            return Result.error("获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除仪器设备（管理员权限）
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> batchDelete(@RequestBody List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return Result.error("请选择要删除的数据");
            }
            instrumentService.batchDelete(ids);
            return Result.success("删除成功");
        } catch (Exception e) {
            log.error("批量删除仪器设备失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取数据统计信息
     */
    @GetMapping("/statistics")
    public Result<Object> getStatistics() {
        try {
            Long guoziCount = instrumentService.countByDataSource("国资系统");
            Long dayiCount = instrumentService.countByDataSource("大仪平台");
            Long totalCount = instrumentService.count();

            return Result.success(new Object() {
                public final Long total = totalCount;
                public final Long guozi = guoziCount;
                public final Long dayi = dayiCount;
            });
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return Result.error("获取统计信息失败：" + e.getMessage());
        }
    }
}