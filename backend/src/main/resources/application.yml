server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: instrument-query-system
  
  # 数据源配置
  datasource:
    url: *****************************************************************************************************************
    username: root
    password: 12345678
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: ${MAX_FILE_SIZE:10MB}
      max-request-size: ${MAX_REQUEST_SIZE:10MB}
      enabled: true
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}
      
  # 跨域配置
  web:
    cors:
      allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://127.0.0.1:3000}
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: Authorization,Content-Type,X-Requested-With
      allow-credentials: true
      max-age: 3600

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  secret: ${JWT_SECRET:yqcx-instrument-query-system-secret-key-2024-default-change-in-production}
  expiration: ${JWT_EXPIRATION:86400000} # 24小时

# 缓存配置
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=30m

# 日志配置
logging:
  level:
    com.yqcx: debug
    org.springframework.security: debug