-- 创建数据库
CREATE DATABASE IF NOT EXISTS yqcx CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE yqcx;

-- 创建仪器设备表
CREATE TABLE IF NOT EXISTS instrument (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    asset_number VARCHAR(100) COMMENT '资产编号',
    device_name VARCHAR(200) NOT NULL COMMENT '设备名称',
    institution VARCHAR(200) COMMENT '所属院校',
    department VARCHAR(200) COMMENT '领用单位',
    price DECIMAL(10,2) COMMENT '单价（万元）',
    brand VARCHAR(100) COMMENT '品牌',
    model VARCHAR(200) COMMENT '型号',
    status VARCHAR(50) COMMENT '使用现状',
    location VARCHAR(200) COMMENT '存放地',
    manufacturer VARCHAR(200) COMMENT '厂家',
    purchase_date VARCHAR(50) COMMENT '购置日期',
    storage_date VARCHAR(50) COMMENT '入库日期',
    usage_2023 INT COMMENT '2023年使用机时（小时）',
    usage_2024 INT COMMENT '2024年使用机时（小时）',
    data_source VARCHAR(20) NOT NULL COMMENT '数据来源（国资系统/大仪平台）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0-未删除，1-已删除）',
    
    INDEX idx_device_name (device_name),
    INDEX idx_model (model),
    INDEX idx_brand (brand),
    INDEX idx_data_source (data_source),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='仪器设备表';

-- 创建用户表
CREATE TABLE IF NOT EXISTS user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    real_name VARCHAR(100) COMMENT '真实姓名',
    role VARCHAR(20) NOT NULL DEFAULT 'USER' COMMENT '角色（ADMIN-管理员，USER-普通用户）',
    status TINYINT DEFAULT 1 COMMENT '状态（0-禁用，1-启用）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除标志（0-未删除，1-已删除）',
    
    INDEX idx_username (username),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 插入默认管理员用户（密码：admin123，已使用BCrypt加密）
INSERT IGNORE INTO user (username, password, real_name, role, status) 
VALUES ('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfcqO9p4MJnGMHWeiUU.2Z/u', '系统管理员', 'ADMIN', 1);

-- 插入一些示例数据
INSERT IGNORE INTO instrument (
    asset_number, device_name, institution, department, price, brand, model, 
    status, location, manufacturer, purchase_date, storage_date, 
    usage_2023, usage_2024, data_source
) VALUES 
('GZ001', '高效液相色谱仪', '清华大学', '化学系', 45.8, 'Agilent', '1260 Infinity', 
 '正常使用', '化学楼301', 'Agilent Technologies', '2020-03-15', '2020-04-01', 
 1200, 1350, '国资系统'),
 
('GZ002', '扫描电子显微镜', '北京大学', '材料学院', 120.5, 'ZEISS', 'Sigma 300', 
 '正常使用', '材料楼B201', 'Carl Zeiss', '2019-08-20', '2019-09-10', 
 800, 950, '国资系统'),
 
('DY001', 'X射线衍射仪', '中科院', '物理所', 85.2, 'Bruker', 'D8 Advance', 
 '正常使用', '实验楼A305', 'Bruker AXS', '2021-01-10', '2021-02-01', 
 1500, 1680, '大仪平台'),
 
('DY002', '核磁共振波谱仪', '复旦大学', '化学系', 200.0, 'Bruker', 'AVANCE III 400', 
 '正常使用', '分析中心', 'Bruker BioSpin', '2018-12-05', '2019-01-15', 
 2200, 2400, '大仪平台'),
 
('GZ003', '气相色谱质谱联用仪', '上海交大', '环境学院', 68.9, 'Thermo', 'TSQ 8000 Evo', 
 '正常使用', '环境楼205', 'Thermo Fisher Scientific', '2020-11-20', '2020-12-10', 
 1100, 1250, '国资系统');