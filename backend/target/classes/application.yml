server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: instrument-query-system
  
  # 数据源配置
  datasource:
    url: *****************************************************************************************************************
    username: root
    password: 12345678
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      
  # 跨域配置
  web:
    cors:
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  secret: yqcx-instrument-query-system-secret-key-2024
  expiration: 86400000 # 24小时

# 日志配置
logging:
  level:
    com.yqcx: debug
    org.springframework.security: debug